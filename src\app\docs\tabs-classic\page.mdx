import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";
import { Table, StatusBadge } from "@/components/ui/table";
import TabsViewClassic from "./tabs-view-classic.tsx";



# 🎨 Tabs View: Classic

The **Classic Tabs** component provides a timeless and functional design, offering a structured and familiar layout that users intuitively understand. Its clean lines and clear separation of content make it a reliable choice for any application where clarity and usability are

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<TabsViewClassic />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/tabs-classic/tabs-view-classic.tsx" />
  </TabsContent>
</Tabs>


# 🚀 Installation
Once installed, import the component and use it within your pages. The component encapsulates a set of pre-defined tabs and content for demonstration.


<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add tabs-classic.json`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/app/docs/tabs-classic/tabs-view-classic.tsx" />
  </TabsContent>
</Tabs>



## 💻 Usage

Once installed, import the component and use it within your pages. The component encapsulates a set of pre-defined tabs and content for demonstration.

```tsx
import { TabsViewClassic } from "@/components/ui/tabs-view-classic"; // Adjust the import path as needed

export default function SettingsPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Account Settings</h1>
      <TabsViewClassic />
    </div>
  );
}
